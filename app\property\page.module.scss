@import '../global';

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;
    padding: 20px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 20px;
        background-color: $white;
        border-radius: $smallBorderRadius;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .title {
            font-size: 24px;
            font-weight: bold;
            color: $primaryColor;
        }
    }

    .listContainer {
        flex: 1;
        background-color: $white;
        border-radius: $smallBorderRadius;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .loadingMessage {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: $darkGrey;
            font-style: italic;
        }
    }

    .clickableRow {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: $lightGrey !important;
        }
    }
}

// Responsive Design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        padding: 10px;

        .header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;

            .title {
                text-align: center;
            }
        }
    }
}
