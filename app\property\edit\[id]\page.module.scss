@import '../../../global';

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;
    padding: 20px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 20px;
        background-color: $white;
        border-radius: $smallBorderRadius;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .title {
            font-size: 24px;
            font-weight: bold;
            color: $primaryColor;
        }
    }

    .contentContainer {
        display: flex;
        flex: 1;
        background-color: $white;
        border-radius: $smallBorderRadius;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        // Left Pane - Property Form
        .leftPane {
            width: 400px;
            min-width: 400px;
            display: flex;
            flex-direction: column;

            .leftPaneHeader {
                padding: 20px;
                border-bottom: 1px solid $lightGrey;
                background-color: $primaryColor;
                color: $white;
                font-weight: bold;
                font-size: 16px;
            }

            .formContainer {
                flex: 1;
                padding: 30px;
                overflow-y: auto;
            }
        }

        // Right Pane - Property Preview/Info
        .rightPane {
            flex: 1;
            display: flex;
            flex-direction: column;

            .rightPaneHeader {
                padding: 20px;
                border-bottom: 1px solid $lightGrey;
                background-color: $secondaryColor;
                color: $white;
                font-weight: bold;
                font-size: 16px;
            }

            .rightPaneContent {
                flex: 1;
                padding: 30px;
                overflow-y: auto;

                .propertyInfo {
                    .infoSection {
                        margin-bottom: 25px;

                        .sectionTitle {
                            font-size: 16px;
                            font-weight: bold;
                            color: $primaryColor;
                            margin-bottom: 10px;
                            border-bottom: 1px solid $lightGrey;
                            padding-bottom: 5px;
                        }

                        .infoGrid {
                            display: grid;
                            grid-template-columns: 1fr 2fr;
                            gap: 10px;

                            .infoLabel {
                                font-weight: 500;
                                color: $darkGrey;
                            }

                            .infoValue {
                                color: $offBlack;
                            }
                        }
                    }
                }
            }

            .noProperty {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                color: $darkGrey;
                font-style: italic;
                font-size: 16px;
            }
        }
    }
}

// Responsive Design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        padding: 10px;

        .header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;

            .title {
                text-align: center;
            }
        }

        .contentContainer {
            flex-direction: column;
            height: auto;

            .leftPane {
                width: 100%;
                min-width: auto;
                max-height: none;
            }

            .rightPane {
                min-height: 400px;
            }
        }
    }
}
