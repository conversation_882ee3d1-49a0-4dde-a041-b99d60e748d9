"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import JC_List from "../components/JC_List/JC_List";
import JC_Button from "../components/JC_Button/JC_Button";
import JC_Title from "../components/JC_Title/JC_Title";
import { PropertyModel } from "../models/Property";
import { O_BuildingTypeModel } from "../models/O_BuildingType";
import { JC_Utils } from "../Utils";
import { JC_ListHeader } from "../components/JC_List/JC_ListHeader";

export default function PropertyPage() {
    const router = useRouter();

    // - STATE - //
    const [properties, setProperties] = useState<PropertyModel[]>([]);
    const [buildingTypes, setBuildingTypes] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // - EFFECTS - //
    useEffect(() => {
        loadData();
    }, []);

    // - LOAD DATA - //
    const loadData = useCallback(async () => {
        try {
            setIsLoading(true);

            // Load properties and building types for display
            const [propertiesData, buildingTypesData] = await Promise.all([
                PropertyModel.GetList(),
                O_BuildingTypeModel.GetList()
            ]);

            setProperties(propertiesData || []);
            setBuildingTypes(buildingTypesData || []);
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // - HANDLERS - //
    const handlePropertyClick = (property: PropertyModel) => {
        router.push(`/property/update?id=${property.Id}`);
    };

    const handleCreateNew = () => {
        router.push('/property/update');
    };

    // - UTILITY FUNCTIONS - //
    const getBuildingTypeName = (code: string) => {
        const buildingType = buildingTypes.find(bt => bt.Code === code);
        return buildingType ? buildingType.Name : 'Not specified';
    };

    // - LIST CONFIGURATION - //
    const listHeaders: JC_ListHeader[] = [
        { sortKey: "Address", label: "Address" },
        { sortKey: "BuildingTypeCode", label: "Building Type" },
        { sortKey: "CompanyStrataTitleCode", label: "Company/Strata", hideOnSmall: true },
        { sortKey: "NumBedroomsCode", label: "Bedrooms", hideOnTiny: true },
        { sortKey: "CreatedAt", label: "Created", hideOnMedium: true }
    ];


    // - BUILD - //
    function _buildPropertyList() {
        return (
            <div className={styles.propertyListContainer}>
                <div className={styles.createButtonContainer}>
                    <JC_Button
                        text="Create New Property"
                        onClick={handleCreateNew}
                        isLoading={isLoading}
                    />
                </div>

                {isLoading ? (
                    <div className={styles.loadingMessage}>Loading properties...</div>
                ) : (
                    <JC_List
                        items={properties}
                        headers={listHeaders}
                        defaultSortKey="Address"
                        defaultSortDirection="asc"
                        row={(property: PropertyModel) => (
                            <tr onClick={() => handlePropertyClick(property)} className={styles.clickableRow}>
                                <td>{property.Address || 'No address'}</td>
                                <td>{getBuildingTypeName(property.BuildingTypeCode || '')}</td>
                                <td>{property.CompanyStrataTitleCode || 'Not specified'}</td>
                                <td>{property.NumBedroomsCode || 'Not specified'}</td>
                                <td>{property.CreatedAt ? new Date(property.CreatedAt).toLocaleDateString() : 'Unknown'}</td>
                            </tr>
                        )}
                    />
                )}
            </div>
        );
    }

    // - RENDER - //
    return (
        <div className={styles.mainContainer}>
            <JC_Title title="Properties" />
            {_buildPropertyList()}
        </div>
    );
}
